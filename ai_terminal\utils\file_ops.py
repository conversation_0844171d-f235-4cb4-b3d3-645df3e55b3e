"""
Advanced file operations and patching utilities.

Provides sophisticated file manipulation capabilities including
unified diff processing, backup management, and safe file operations.
"""

import asyncio
import shutil
import tempfile
from typing import List, Optional, Dict, Any, Tuple
from pathlib import Path
from datetime import datetime
import difflib
import re

import aiofiles

from ai_terminal.utils.logger import get_logger
from ai_terminal.config.settings import get_settings

logger = get_logger(__name__)


class FileOperations:
    """Advanced file operations manager."""
    
    def __init__(self):
        """Initialize file operations."""
        self.settings = get_settings()
        self.backup_dir = self.settings.data_dir / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    async def read_file_safe(
        self,
        file_path: Path,
        encoding: str = "utf-8",
        max_size: Optional[int] = None
    ) -> str:
        """Safely read a file with size and encoding checks.
        
        Args:
            file_path: Path to file
            encoding: File encoding
            max_size: Maximum file size in bytes
            
        Returns:
            File content as string
            
        Raises:
            FileNotFoundError: If file doesn't exist
            ValueError: If file is too large or invalid
        """
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if not file_path.is_file():
            raise ValueError(f"Path is not a file: {file_path}")
        
        # Check file size
        file_size = file_path.stat().st_size
        max_allowed = max_size or self.settings.security.max_file_size
        
        if file_size > max_allowed:
            raise ValueError(f"File too large: {file_size} bytes (max: {max_allowed})")
        
        # Check file extension
        if (file_path.suffix and 
            file_path.suffix not in self.settings.security.allowed_extensions):
            logger.warning(f"Reading file with non-allowed extension: {file_path}")
        
        try:
            async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                content = await f.read()
            
            logger.debug(f"Read file: {file_path} ({file_size} bytes)")
            return content
            
        except UnicodeDecodeError as e:
            raise ValueError(f"Failed to decode file with {encoding}: {e}")
    
    async def write_file_safe(
        self,
        file_path: Path,
        content: str,
        encoding: str = "utf-8",
        create_backup: bool = True,
        create_dirs: bool = False
    ) -> bool:
        """Safely write content to a file with backup.
        
        Args:
            file_path: Path to file
            content: Content to write
            encoding: File encoding
            create_backup: Create backup if file exists
            create_dirs: Create parent directories
            
        Returns:
            True if successful
            
        Raises:
            ValueError: If operation is not safe
        """
        # Security checks
        if not self._is_path_safe(file_path):
            raise ValueError(f"Unsafe file path: {file_path}")
        
        # Create parent directories if requested
        if create_dirs:
            file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create backup if file exists
        if create_backup and file_path.exists():
            await self._create_backup(file_path)
        
        try:
            async with aiofiles.open(file_path, 'w', encoding=encoding) as f:
                await f.write(content)
            
            logger.info(f"Wrote file: {file_path} ({len(content)} chars)")
            return True
            
        except Exception as e:
            logger.error(f"Failed to write file {file_path}: {e}")
            raise
    
    async def apply_patch(
        self,
        file_path: Path,
        patch_content: str,
        backup: bool = True
    ) -> Dict[str, Any]:
        """Apply a unified diff patch to a file.
        
        Args:
            file_path: Path to file to patch
            patch_content: Unified diff content
            backup: Create backup before patching
            
        Returns:
            Dictionary with patch results
        """
        try:
            # Read original file
            if file_path.exists():
                original_content = await self.read_file_safe(file_path)
                original_lines = original_content.splitlines(keepends=True)
            else:
                original_lines = []
            
            # Parse patch
            patch_lines = patch_content.splitlines()
            
            # Apply patch using difflib
            try:
                # Simple patch application (could be enhanced)
                patched_lines = self._apply_unified_diff(original_lines, patch_lines)
                patched_content = ''.join(patched_lines)
                
                # Create backup if requested
                if backup and file_path.exists():
                    backup_path = await self._create_backup(file_path)
                else:
                    backup_path = None
                
                # Write patched content
                await self.write_file_safe(
                    file_path,
                    patched_content,
                    create_backup=False  # Already created backup
                )
                
                return {
                    "success": True,
                    "file_path": str(file_path),
                    "backup_path": str(backup_path) if backup_path else None,
                    "lines_added": len(patched_lines) - len(original_lines),
                    "patch_applied": True,
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "error": f"Failed to apply patch: {e}",
                    "file_path": str(file_path),
                }
                
        except Exception as e:
            logger.error(f"Patch application failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_path": str(file_path),
            }
    
    async def create_diff(
        self,
        original_content: str,
        modified_content: str,
        filename: str = "file"
    ) -> str:
        """Create a unified diff between two content strings.
        
        Args:
            original_content: Original content
            modified_content: Modified content
            filename: Filename for diff headers
            
        Returns:
            Unified diff string
        """
        original_lines = original_content.splitlines(keepends=True)
        modified_lines = modified_content.splitlines(keepends=True)
        
        diff = difflib.unified_diff(
            original_lines,
            modified_lines,
            fromfile=f"a/{filename}",
            tofile=f"b/{filename}",
            lineterm=""
        )
        
        return ''.join(diff)
    
    async def compare_files(
        self,
        file1_path: Path,
        file2_path: Path
    ) -> Dict[str, Any]:
        """Compare two files and return diff information.
        
        Args:
            file1_path: Path to first file
            file2_path: Path to second file
            
        Returns:
            Comparison results
        """
        try:
            # Read both files
            content1 = await self.read_file_safe(file1_path)
            content2 = await self.read_file_safe(file2_path)
            
            # Create diff
            diff = await self.create_diff(
                content1,
                content2,
                filename=file1_path.name
            )
            
            # Calculate statistics
            lines1 = content1.splitlines()
            lines2 = content2.splitlines()
            
            return {
                "files_identical": content1 == content2,
                "file1": str(file1_path),
                "file2": str(file2_path),
                "file1_lines": len(lines1),
                "file2_lines": len(lines2),
                "diff": diff,
                "has_differences": bool(diff.strip()),
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "file1": str(file1_path),
                "file2": str(file2_path),
            }
    
    async def _create_backup(self, file_path: Path) -> Path:
        """Create a backup of a file.
        
        Args:
            file_path: Path to file to backup
            
        Returns:
            Path to backup file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.name}.{timestamp}.backup"
        backup_path = self.backup_dir / backup_name
        
        # Copy file to backup location
        shutil.copy2(file_path, backup_path)
        
        logger.debug(f"Created backup: {backup_path}")
        return backup_path
    
    def _apply_unified_diff(
        self,
        original_lines: List[str],
        patch_lines: List[str]
    ) -> List[str]:
        """Apply a unified diff patch to lines.
        
        This is a simplified implementation. For production use,
        consider using a more robust patching library.
        """
        result_lines = original_lines.copy()
        
        # Parse patch headers and hunks
        i = 0
        while i < len(patch_lines):
            line = patch_lines[i]
            
            # Skip non-hunk lines
            if not line.startswith('@@'):
                i += 1
                continue
            
            # Parse hunk header
            hunk_match = re.match(r'@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@', line)
            if not hunk_match:
                i += 1
                continue
            
            old_start = int(hunk_match.group(1)) - 1  # Convert to 0-based
            old_count = int(hunk_match.group(2)) if hunk_match.group(2) else 1
            new_start = int(hunk_match.group(3)) - 1  # Convert to 0-based
            new_count = int(hunk_match.group(4)) if hunk_match.group(4) else 1
            
            # Apply hunk
            i += 1
            hunk_lines = []
            
            while i < len(patch_lines) and not patch_lines[i].startswith('@@'):
                hunk_line = patch_lines[i]
                if hunk_line.startswith(' '):
                    # Context line
                    hunk_lines.append(('context', hunk_line[1:]))
                elif hunk_line.startswith('-'):
                    # Deletion
                    hunk_lines.append(('delete', hunk_line[1:]))
                elif hunk_line.startswith('+'):
                    # Addition
                    hunk_lines.append(('add', hunk_line[1:]))
                i += 1
            
            # Apply hunk to result_lines
            result_lines = self._apply_hunk(result_lines, old_start, hunk_lines)
        
        return result_lines
    
    def _apply_hunk(
        self,
        lines: List[str],
        start_line: int,
        hunk_lines: List[Tuple[str, str]]
    ) -> List[str]:
        """Apply a single hunk to lines."""
        result = lines[:start_line]
        line_index = start_line
        
        for action, content in hunk_lines:
            if action == 'context':
                result.append(content)
                line_index += 1
            elif action == 'delete':
                # Skip the line (delete it)
                line_index += 1
            elif action == 'add':
                result.append(content)
        
        # Add remaining lines
        result.extend(lines[line_index:])
        
        return result
    
    def _is_path_safe(self, path: Path) -> bool:
        """Check if a file path is safe for operations."""
        try:
            # Resolve path and check if it's within allowed areas
            resolved_path = path.resolve()
            
            # Check for path traversal
            cwd = Path.cwd().resolve()
            try:
                resolved_path.relative_to(cwd)
            except ValueError:
                # Path is outside current directory
                logger.warning(f"Path outside working directory: {path}")
                return False
            
            # Check for dangerous paths
            dangerous_paths = [
                "/etc", "/bin", "/sbin", "/usr/bin", "/usr/sbin",
                "/boot", "/sys", "/proc", "/dev"
            ]
            
            path_str = str(resolved_path)
            for dangerous in dangerous_paths:
                if path_str.startswith(dangerous):
                    logger.warning(f"Dangerous path detected: {path}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Path safety check failed: {e}")
            return False
    
    async def cleanup_backups(self, days: int = 7) -> int:
        """Clean up old backup files.
        
        Args:
            days: Delete backups older than this many days
            
        Returns:
            Number of backups deleted
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
            deleted_count = 0
            
            for backup_file in self.backup_dir.glob("*.backup"):
                if backup_file.stat().st_mtime < cutoff_time:
                    backup_file.unlink()
                    deleted_count += 1
            
            logger.info(f"Cleaned up {deleted_count} old backup files")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Backup cleanup failed: {e}")
            return 0
