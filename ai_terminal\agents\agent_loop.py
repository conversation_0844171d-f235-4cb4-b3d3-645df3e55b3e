"""
Core agent loop for AI interactions.

Handles conversation management, tool execution, and streaming responses
with support for multiple AI providers and advanced features.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, AsyncIterator, Callable
from datetime import datetime
import uuid

from ai_terminal.providers.base import (
    BaseProvider,
    Message,
    MessageRole,
    StreamChunk,
    CompletionResponse,
    ProviderError,
)
from ai_terminal.agents.context import ConversationContext
from ai_terminal.agents.tools import Tool<PERSON><PERSON><PERSON><PERSON>, ToolResult
from ai_terminal.security.approval import ApprovalManager
from ai_terminal.utils.logger import get_logger, log_performance
from ai_terminal.config.settings import get_settings

logger = get_logger(__name__)


class AgentLoop:
    """Core agent loop for AI interactions."""

    def __init__(
        self,
        provider: BaseProvider,
        tool_registry: Optional[ToolRegistry] = None,
        approval_manager: Optional[ApprovalManager] = None,
        session_manager: Optional[Any] = None,  # Use Any to avoid circular import
    ):
        """Initialize the agent loop.
        
        Args:
            provider: AI provider instance
            tool_registry: Tool registry for function calling
            approval_manager: Approval manager for security
            session_manager: Session manager for persistence
        """
        self.provider = provider
        self.tool_registry = tool_registry or ToolRegistry()
        self.approval_manager = approval_manager
        self.session_manager = session_manager
        self.settings = get_settings()
        
        # Current conversation context
        self.context: Optional[ConversationContext] = None
        
        # Event callbacks
        self.on_message_start: Optional[Callable[[Message], None]] = None
        self.on_message_chunk: Optional[Callable[[str], None]] = None
        self.on_message_complete: Optional[Callable[[Message], None]] = None
        self.on_tool_call: Optional[Callable[[str, Dict[str, Any]], None]] = None
        self.on_tool_result: Optional[Callable[[str, ToolResult], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
    
    async def start_conversation(
        self,
        system_message: Optional[str] = None,
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        session_id: Optional[str] = None,
    ) -> ConversationContext:
        """Start a new conversation or resume an existing one.
        
        Args:
            system_message: System message to set context
            model: Model to use for this conversation
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            session_id: Existing session ID to resume
            
        Returns:
            ConversationContext for the session
        """
        if session_id and self.session_manager:
            # Try to resume existing session
            try:
                self.context = await self.session_manager.load_session(session_id)
                logger.info(f"Resumed conversation session: {session_id}")
            except Exception as e:
                logger.warning(f"Failed to resume session {session_id}: {e}")
                self.context = None
        
        if not self.context:
            # Create new conversation
            self.context = ConversationContext(
                session_id=session_id or str(uuid.uuid4()),
                provider=self.provider,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                system_message=system_message,
            )
            logger.info(f"Started new conversation session: {self.context.session_id}")
        
        return self.context
    
    async def send_message(
        self,
        content: str,
        role: MessageRole = MessageRole.USER,
        stream: bool = True,
        files: Optional[List[str]] = None,
    ) -> AsyncIterator[StreamChunk]:
        """Send a message and get AI response.
        
        Args:
            content: Message content
            role: Message role
            stream: Whether to stream the response
            files: Optional list of file paths to include
            
        Yields:
            StreamChunk objects with response content
        """
        if not self.context:
            raise ValueError("No active conversation. Call start_conversation() first.")
        
        # Process file attachments
        if files:
            content = await self._process_file_attachments(content, files)
        
        # Add user message to context
        user_message = Message(role=role, content=content)
        self.context.add_message(user_message)
        
        if self.on_message_start:
            self.on_message_start(user_message)
        
        # Save session if manager is available
        if self.session_manager:
            await self.session_manager.save_session(self.context)
        
        # Get AI response
        try:
            if stream:
                async for chunk in self._stream_response():
                    yield chunk
            else:
                response = await self._complete_response()
                yield StreamChunk(
                    content=response.content,
                    is_complete=True,
                    metadata=response.metadata,
                )
        except Exception as e:
            logger.error(f"Failed to get AI response: {e}", exc_info=True)
            if self.on_error:
                self.on_error(e)
            raise
    
    async def _stream_response(self) -> AsyncIterator[StreamChunk]:
        """Stream AI response with tool calling support."""
        messages = self.context.get_messages_for_api()
        tools = self.tool_registry.get_tool_definitions() if self.tool_registry else None
        
        with log_performance(logger, "stream_completion", model=self.context.model):
            accumulated_content = ""
            accumulated_tool_calls = []
            
            async for chunk in self.provider.stream_complete(
                messages=messages,
                model=self.context.model,
                temperature=self.context.temperature,
                max_tokens=self.context.max_tokens,
                tools=tools,
            ):
                # Handle content chunks
                if chunk.content:
                    accumulated_content += chunk.content
                    if self.on_message_chunk:
                        self.on_message_chunk(chunk.content)
                    yield chunk
                
                # Handle tool calls
                if chunk.tool_calls:
                    accumulated_tool_calls.extend(chunk.tool_calls)
                
                # Handle completion
                if chunk.is_complete:
                    # Create assistant message
                    assistant_message = Message(
                        role=MessageRole.ASSISTANT,
                        content=accumulated_content,
                        tool_calls=accumulated_tool_calls if accumulated_tool_calls else None,
                    )
                    self.context.add_message(assistant_message)
                    
                    if self.on_message_complete:
                        self.on_message_complete(assistant_message)
                    
                    # Execute tool calls if present
                    if accumulated_tool_calls:
                        async for tool_chunk in self._execute_tool_calls(accumulated_tool_calls):
                            yield tool_chunk
                    
                    # Save session
                    if self.session_manager:
                        await self.session_manager.save_session(self.context)
                    
                    yield chunk
                    break
    
    async def _complete_response(self) -> CompletionResponse:
        """Get complete AI response (non-streaming)."""
        messages = self.context.get_messages_for_api()
        tools = self.tool_registry.get_tool_definitions() if self.tool_registry else None
        
        with log_performance(logger, "completion", model=self.context.model):
            response = await self.provider.complete(
                messages=messages,
                model=self.context.model,
                temperature=self.context.temperature,
                max_tokens=self.context.max_tokens,
                tools=tools,
            )
        
        # Add assistant message to context
        assistant_message = Message(
            role=MessageRole.ASSISTANT,
            content=response.content,
            tool_calls=response.tool_calls,
        )
        self.context.add_message(assistant_message)
        
        if self.on_message_complete:
            self.on_message_complete(assistant_message)
        
        # Execute tool calls if present
        if response.tool_calls:
            await self._execute_tool_calls_sync(response.tool_calls)
        
        # Save session
        if self.session_manager:
            await self.session_manager.save_session(self.context)
        
        return response
    
    async def _execute_tool_calls(self, tool_calls: List[Dict[str, Any]]) -> AsyncIterator[StreamChunk]:
        """Execute tool calls and yield results."""
        for tool_call in tool_calls:
            tool_name = tool_call["function"]["name"]
            tool_args = json.loads(tool_call["function"]["arguments"])
            tool_id = tool_call.get("id", str(uuid.uuid4()))
            
            if self.on_tool_call:
                self.on_tool_call(tool_name, tool_args)
            
            # Check approval if required
            if self.approval_manager:
                approved = await self.approval_manager.request_approval(
                    tool_name, tool_args, self.context
                )
                if not approved:
                    result = ToolResult(
                        success=False,
                        content="Tool execution denied by approval policy",
                        metadata={"tool": tool_name, "reason": "approval_denied"}
                    )
                    yield StreamChunk(
                        content=f"\n[Tool execution denied: {tool_name}]\n",
                        is_complete=False,
                        metadata={"tool_result": result.dict()}
                    )
                    continue
            
            # Execute tool
            try:
                result = await self.tool_registry.execute_tool(tool_name, tool_args)
                
                if self.on_tool_result:
                    self.on_tool_result(tool_name, result)
                
                # Add tool result message
                tool_message = Message(
                    role=MessageRole.TOOL,
                    content=result.content,
                    name=tool_name,
                )
                self.context.add_message(tool_message)
                
                # Yield tool result
                yield StreamChunk(
                    content=f"\n[Tool: {tool_name}]\n{result.content}\n",
                    is_complete=False,
                    metadata={"tool_result": result.dict()}
                )
                
            except Exception as e:
                logger.error(f"Tool execution failed: {tool_name}: {e}", exc_info=True)
                error_result = ToolResult(
                    success=False,
                    content=f"Tool execution failed: {e}",
                    metadata={"tool": tool_name, "error": str(e)}
                )
                
                yield StreamChunk(
                    content=f"\n[Tool error: {tool_name}] {e}\n",
                    is_complete=False,
                    metadata={"tool_result": error_result.dict()}
                )
    
    async def _execute_tool_calls_sync(self, tool_calls: List[Dict[str, Any]]) -> None:
        """Execute tool calls synchronously (for non-streaming mode)."""
        async for _ in self._execute_tool_calls(tool_calls):
            pass  # Just execute, don't yield
    
    async def _process_file_attachments(self, content: str, files: List[str]) -> str:
        """Process file attachments and include them in the message."""
        # This would integrate with the file operations utility
        # For now, just mention the files
        file_list = ", ".join(files)
        return f"{content}\n\nAttached files: {file_list}"
    
    async def clear_conversation(self) -> None:
        """Clear the current conversation."""
        if self.context:
            self.context.messages.clear()
            self.context.updated_at = datetime.now()
            
            if self.session_manager:
                await self.session_manager.save_session(self.context)
    
    async def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation."""
        if not self.context or not self.context.messages:
            return "No conversation history."
        
        message_count = len(self.context.messages)
        user_messages = len([m for m in self.context.messages if m.role == MessageRole.USER])
        assistant_messages = len([m for m in self.context.messages if m.role == MessageRole.ASSISTANT])
        
        return (
            f"Session: {self.context.session_id}\n"
            f"Messages: {message_count} total ({user_messages} user, {assistant_messages} assistant)\n"
            f"Model: {self.context.model or 'default'}\n"
            f"Started: {self.context.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Updated: {self.context.updated_at.strftime('%Y-%m-%d %H:%M:%S')}"
        )
