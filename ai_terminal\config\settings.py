"""
Core configuration management using Pydantic settings.

Provides a hierarchical configuration system that loads from:
1. Environment variables
2. CLI arguments  
3. Configuration files (YAML/JSON)
4. Default values
"""

import os
import platform
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class UISettings(BaseSettings):
    """User interface configuration."""
    
    theme: str = Field(default="dark", description="UI theme (dark/light)")
    streaming: bool = Field(default=True, description="Enable streaming responses")
    syntax_highlighting: bool = Field(default=True, description="Enable syntax highlighting")
    compact_mode: bool = Field(default=False, description="Use compact display mode")
    max_history_display: int = Field(default=50, description="Max history items to display")
    
    class Config:
        env_prefix = "AI_TERMINAL_UI_"


class SecuritySettings(BaseSettings):
    """Security and sandboxing configuration."""
    
    sandbox_enabled: bool = Field(default=True, description="Enable command sandboxing")
    approval_required: List[str] = Field(
        default_factory=lambda: ["rm", "sudo", "chmod", "chown", "mv", "cp"],
        description="Commands requiring approval"
    )
    approval_patterns: List[str] = Field(
        default_factory=lambda: [r"rm\s+-rf", r"sudo\s+rm", r">\s*/dev/"],
        description="Regex patterns requiring approval"
    )
    max_file_size: int = Field(default=10_000_000, description="Max file size for operations (bytes)")
    allowed_extensions: List[str] = Field(
        default_factory=lambda: [".py", ".js", ".ts", ".md", ".txt", ".json", ".yaml", ".yml"],
        description="Allowed file extensions for operations"
    )
    
    class Config:
        env_prefix = "AI_TERMINAL_SECURITY_"


class LoggingSettings(BaseSettings):
    """Logging configuration."""
    
    level: str = Field(default="INFO", description="Log level")
    file_enabled: bool = Field(default=True, description="Enable file logging")
    console_enabled: bool = Field(default=True, description="Enable console logging")
    max_file_size: int = Field(default=10_000_000, description="Max log file size (bytes)")
    backup_count: int = Field(default=5, description="Number of backup log files")
    
    class Config:
        env_prefix = "AI_TERMINAL_LOG_"


class Settings(BaseSettings):
    """Main application settings."""
    
    # Core settings
    app_name: str = Field(default="ai-terminal", description="Application name")
    version: str = Field(default="0.1.0", description="Application version")
    debug: bool = Field(default=False, description="Enable debug mode")
    
    # Provider settings
    default_provider: str = Field(default="openai", description="Default AI provider")
    default_model: str = Field(default="gpt-4", description="Default AI model")
    
    # API Keys (loaded from environment)
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API key")
    deepseek_api_key: Optional[str] = Field(default=None, description="Deepseek API key")
    
    # Paths
    config_dir: Path = Field(default_factory=lambda: Settings._get_config_dir())
    data_dir: Path = Field(default_factory=lambda: Settings._get_data_dir())
    cache_dir: Path = Field(default_factory=lambda: Settings._get_cache_dir())
    
    # Nested settings
    ui: UISettings = Field(default_factory=UISettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    
    # Provider configurations
    providers: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        env_prefix = "AI_TERMINAL_"
        env_file = ".env"
        case_sensitive = False
        
    @staticmethod
    def _get_config_dir() -> Path:
        """Get platform-specific configuration directory."""
        if platform.system() == "Windows":
            base = Path(os.environ.get("APPDATA", "~"))
        elif platform.system() == "Darwin":  # macOS
            base = Path("~/Library/Application Support")
        else:  # Linux and others
            base = Path(os.environ.get("XDG_CONFIG_HOME", "~/.config"))
        
        return (base / "ai-terminal").expanduser()
    
    @staticmethod
    def _get_data_dir() -> Path:
        """Get platform-specific data directory."""
        if platform.system() == "Windows":
            base = Path(os.environ.get("LOCALAPPDATA", "~"))
        elif platform.system() == "Darwin":  # macOS
            base = Path("~/Library/Application Support")
        else:  # Linux and others
            base = Path(os.environ.get("XDG_DATA_HOME", "~/.local/share"))
        
        return (base / "ai-terminal").expanduser()
    
    @staticmethod
    def _get_cache_dir() -> Path:
        """Get platform-specific cache directory."""
        if platform.system() == "Windows":
            base = Path(os.environ.get("TEMP", "~"))
        elif platform.system() == "Darwin":  # macOS
            base = Path("~/Library/Caches")
        else:  # Linux and others
            base = Path(os.environ.get("XDG_CACHE_HOME", "~/.cache"))
        
        return (base / "ai-terminal").expanduser()
    
    @validator("providers", pre=True)
    def validate_providers(cls, v):
        """Validate and set default provider configurations."""
        if not v:
            v = {}
        
        # Set default provider configurations
        defaults = {
            "openai": {
                "base_url": "https://api.openai.com/v1",
                "models": ["gpt-4", "gpt-3.5-turbo", "gpt-4-turbo-preview"],
                "supports_functions": True,
                "supports_streaming": True,
            },
            "deepseek": {
                "base_url": "https://api.deepseek.com/v1",
                "models": ["deepseek-coder", "deepseek-chat"],
                "supports_functions": True,
                "supports_streaming": True,
            },
            "anthropic": {
                "base_url": "https://api.anthropic.com",
                "models": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
                "supports_functions": True,
                "supports_streaming": True,
            },
            "ollama": {
                "base_url": "http://localhost:11434",
                "models": ["llama2", "codellama", "mistral"],
                "supports_functions": False,
                "supports_streaming": True,
            }
        }
        
        # Merge with user configurations
        for provider, config in defaults.items():
            if provider not in v:
                v[provider] = {}
            v[provider] = {**config, **v[provider]}
        
        return v
    
    def ensure_directories(self) -> None:
        """Ensure all required directories exist."""
        for directory in [self.config_dir, self.data_dir, self.cache_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_provider_config(self, provider: str) -> Dict[str, Any]:
        """Get configuration for a specific provider."""
        return self.providers.get(provider, {})
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Get API key for a specific provider."""
        key_mapping = {
            "openai": self.openai_api_key,
            "deepseek": self.deepseek_api_key,
            "anthropic": self.anthropic_api_key,
        }
        return key_mapping.get(provider)
    
    def save_to_file(self, file_path: Optional[Path] = None) -> None:
        """Save current settings to a configuration file."""
        import yaml
        
        if file_path is None:
            file_path = self.config_dir / "config.yaml"
        
        # Ensure directory exists
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert to dict and save
        config_dict = self.dict(exclude={"openai_api_key", "anthropic_api_key", "deepseek_api_key"})
        
        with open(file_path, "w") as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    @classmethod
    def load_from_file(cls, file_path: Path) -> "Settings":
        """Load settings from a configuration file."""
        import yaml
        
        if not file_path.exists():
            return cls()
        
        with open(file_path, "r") as f:
            config_dict = yaml.safe_load(f) or {}
        
        return cls(**config_dict)


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get the global settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings()
        _settings.ensure_directories()
    return _settings


def reload_settings() -> Settings:
    """Reload settings from configuration files and environment."""
    global _settings
    _settings = None
    return get_settings()
