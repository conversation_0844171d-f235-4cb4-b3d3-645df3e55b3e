"""
Abstract base provider interface for AI providers.

Defines the common interface that all AI providers must implement,
ensuring consistent behavior across different AI services.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, AsyncIterator, Union
from dataclasses import dataclass
from enum import Enum
import asyncio
from datetime import datetime


class MessageRole(str, Enum):
    """Message roles in a conversation."""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"
    TOOL = "tool"


@dataclass
class Message:
    """A message in a conversation."""
    role: MessageRole
    content: str
    name: Optional[str] = None
    function_call: Optional[Dict[str, Any]] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class StreamChunk:
    """A chunk of streaming response."""
    content: str
    is_complete: bool = False
    function_call: Optional[Dict[str, Any]] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class CompletionResponse:
    """Complete response from an AI provider."""
    content: str
    messages: List[Message]
    usage: Optional[Dict[str, Any]] = None
    function_calls: Optional[List[Dict[str, Any]]] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None


@dataclass
class ProviderCapabilities:
    """Capabilities of an AI provider."""
    supports_streaming: bool = True
    supports_functions: bool = False
    supports_tools: bool = False
    supports_vision: bool = False
    supports_system_messages: bool = True
    max_context_length: int = 4096
    max_output_tokens: int = 2048


class BaseProvider(ABC):
    """Abstract base class for AI providers."""
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model: Optional[str] = None,
        **kwargs
    ):
        """Initialize the provider.
        
        Args:
            api_key: API key for the provider
            base_url: Base URL for the API
            model: Default model to use
            **kwargs: Additional provider-specific configuration
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.config = kwargs
        self._client = None
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Provider name."""
        pass
    
    @property
    @abstractmethod
    def capabilities(self) -> ProviderCapabilities:
        """Provider capabilities."""
        pass
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the provider (create client, validate credentials, etc.)."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources (close connections, etc.)."""
        pass
    
    @abstractmethod
    async def list_models(self) -> List[str]:
        """List available models for this provider."""
        pass
    
    @abstractmethod
    async def complete(
        self,
        messages: List[Message],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> CompletionResponse:
        """Generate a completion for the given messages.
        
        Args:
            messages: List of conversation messages
            model: Model to use (overrides default)
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            functions: Available functions for function calling
            tools: Available tools for tool calling
            **kwargs: Additional provider-specific parameters
            
        Returns:
            CompletionResponse with the generated content
        """
        pass
    
    @abstractmethod
    async def stream_complete(
        self,
        messages: List[Message],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncIterator[StreamChunk]:
        """Generate a streaming completion for the given messages.
        
        Args:
            messages: List of conversation messages
            model: Model to use (overrides default)
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            functions: Available functions for function calling
            tools: Available tools for tool calling
            **kwargs: Additional provider-specific parameters
            
        Yields:
            StreamChunk objects with incremental content
        """
        pass
    
    async def validate_connection(self) -> bool:
        """Validate that the provider connection is working.
        
        Returns:
            True if connection is valid, False otherwise
        """
        try:
            models = await self.list_models()
            return len(models) > 0
        except Exception:
            return False
    
    def format_messages(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """Format messages for the provider's API format.
        
        Args:
            messages: List of Message objects
            
        Returns:
            List of dictionaries in provider's expected format
        """
        formatted = []
        for msg in messages:
            formatted_msg = {
                "role": msg.role.value,
                "content": msg.content,
            }
            
            if msg.name:
                formatted_msg["name"] = msg.name
            if msg.function_call:
                formatted_msg["function_call"] = msg.function_call
            if msg.tool_calls:
                formatted_msg["tool_calls"] = msg.tool_calls
                
            formatted.append(formatted_msg)
        
        return formatted
    
    def parse_message(self, response_data: Dict[str, Any]) -> Message:
        """Parse a message from provider response data.
        
        Args:
            response_data: Raw response data from provider
            
        Returns:
            Parsed Message object
        """
        role = MessageRole(response_data.get("role", "assistant"))
        content = response_data.get("content", "")
        
        return Message(
            role=role,
            content=content,
            name=response_data.get("name"),
            function_call=response_data.get("function_call"),
            tool_calls=response_data.get("tool_calls"),
        )
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()


class ProviderError(Exception):
    """Base exception for provider errors."""
    
    def __init__(self, message: str, provider: str, error_code: Optional[str] = None):
        super().__init__(message)
        self.provider = provider
        self.error_code = error_code


class AuthenticationError(ProviderError):
    """Authentication failed with the provider."""
    pass


class RateLimitError(ProviderError):
    """Rate limit exceeded."""
    
    def __init__(self, message: str, provider: str, retry_after: Optional[int] = None):
        super().__init__(message, provider)
        self.retry_after = retry_after


class ModelNotFoundError(ProviderError):
    """Requested model not found."""
    pass


class InvalidRequestError(ProviderError):
    """Invalid request parameters."""
    pass


class ServiceUnavailableError(ProviderError):
    """Provider service is unavailable."""
    pass
